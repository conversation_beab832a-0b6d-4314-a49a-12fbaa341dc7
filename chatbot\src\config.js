const CONFIG = {
    // Avatar Configuration
    DEFAULT_AVATAR_NAME: "Assistant", // Default name for the avatar

    // API Configuration
    USE_LOCAL_RESPONSES: false, // Always use API calls
    VOICE_ENABLED: true,
    DEFAULT_VOICE: 'en-US-Standard-A',

    // Gemini API Configuration
    API: {
        GEMINI_API_KEY: "AIzaSyBud8o323uriIZFMYFXwX985ch9sCJRoOg", // Gemini API key
        GEMINI_API_URL: "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        MAX_TOKENS: 200,
        TEMPERATURE: 0.7
    },

    // Project Information (to be sent with each API request)
    PROJECT_INFO: `This is a 3D avatar project with lip-sync capabilities built using React Three Fiber (R3F).
    The project features a talking avatar that can respond to user messages and animate its mouth in sync with speech.
    The lip-sync works by mapping text or audio to visemes (visual representations of phonemes).
    The avatar's mouth shape changes based on the sounds being spoken, using morph targets in the 3D model.
    The project uses React for the UI, Three.js for 3D rendering, and React Three Fiber as a React wrapper for Three.js.
    It also uses the Web Speech API for text-to-speech functionality.`,

    // Avatar appearance
    AVATAR: {
        HEAD_COLOR: 0xffceb4, // Skin tone
        EYE_COLOR: 0x000000,
        MOUTH_COLOR: 0xe74c3c,
        ANIMATION_SPEED: 0.5,
        FRIENDLY_BLUE: 0x3a7bd5,
        DARK_BLUE: 0x2d5a9c,
        LIGHT_BLUE: 0xe1f0ff,
        HEART_COLOR: 0xff6b6b
    }
};

export default CONFIG;
